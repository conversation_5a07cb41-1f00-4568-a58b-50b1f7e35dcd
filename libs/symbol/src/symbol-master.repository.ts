import { Injectable, Logger, OnModuleInit } from '@nestjs/common';
import { QuestDBService } from '@app/core/questdb';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { QuestDBError } from '@app/core/questdb/questdb.error';
import { 
  SymbolMaster, 
  SymbolMasterUpsert, 
  SymbolMasterQueryFilters,
  SymbolMasterSchema,
  SymbolMasterUpsertSchema,
  SymbolMasterQueryFiltersSchema,
  KiteInstrumentMasterRaw,
  KiteInstrumentMasterRawSchema,
} from './symbol.schema';
import { 
  SymbolMasterQuestDBTable, 
  SymbolMasterQueries, 
  SYMBOL_MASTER_TABLE 
} from './symbol-master.questdb';

/**
 * Symbol Master Repository for QuestDB operations
 * 
 * Provides high-performance time-series database operations for symbol master data
 * using QuestDB. Implements idempotent upsert operations with deduplication,
 * batch processing capabilities, and optimized querying for symbol data.
 * 
 * Features:
 * - Idempotent upsert operations with automatic deduplication
 * - Batch insert/update operations for high-volume data
 * - Time-series optimized queries and storage
 * - Comprehensive error handling and logging
 * - Data validation using Zod schemas
 * - Performance monitoring and statistics
 */
@Injectable()
export class SymbolMasterRepository implements OnModuleInit {
  private readonly logger = new Logger(SymbolMasterRepository.name);
  private readonly tableManager: SymbolMasterQuestDBTable;

  constructor(
    private readonly questdbService: QuestDBService,
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
  ) {
    this.tableManager = new SymbolMasterQuestDBTable(questdbService);
  }

  /**
   * Initialize the repository and ensure table exists
   */
  async onModuleInit(): Promise<void> {
    try {
      this.logger.log('Initializing Symbol Master Repository');
      await this.tableManager.initialize();
      
      const isValid = await this.tableManager.validateTable();
      if (!isValid) {
        throw new Error('Symbol master table validation failed');
      }
      
      this.logger.log('Symbol Master Repository initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize Symbol Master Repository', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw error;
    }
  }

  // ==================== UPSERT OPERATIONS ====================

  /**
   * Upsert a single symbol master record
   * Uses QuestDB's DEDUP UPSERT KEYS for idempotent operations
   */
  async upsertSymbol(symbolData: SymbolMasterUpsert): Promise<{ success: boolean; isNew: boolean }> {
    try {
      // Validate input data
      const validatedData = SymbolMasterUpsertSchema.parse(symbolData);
      
      this.logger.debug('Upserting symbol', {
        instrumentToken: validatedData.instrumentToken,
        tradingSymbol: validatedData.tradingSymbol,
      });

      const now = this.dateTimeUtils.getUtcNow();
      
      // Check if symbol exists
      const existing = await this.findByInstrumentToken(validatedData.instrumentToken);
      const isNew = !existing;

      // Prepare upsert data
      const upsertData = [
        validatedData.instrumentToken,
        validatedData.exchangeToken,
        validatedData.tradingSymbol,
        validatedData.name,
        validatedData.lastPrice,
        validatedData.tickSize,
        validatedData.lotSize,
        validatedData.expiry ? validatedData.expiry.toISOString() : null,
        validatedData.strike,
        validatedData.instrumentType,
        validatedData.segment,
        validatedData.exchange,
        validatedData.isActive,
        now.toISOString(), // downloaded_at
        now.toISOString(), // updated_at
        now.toISOString(), // timestamp (designated timestamp column)
      ];

      await this.questdbService.executeQuery(SymbolMasterQueries.UPSERT_SYMBOL, upsertData);

      this.logger.debug(`Symbol ${isNew ? 'added' : 'updated'} successfully`, {
        instrumentToken: validatedData.instrumentToken,
        tradingSymbol: validatedData.tradingSymbol,
        isNew,
      });

      return { success: true, isNew };
    } catch (error) {
      this.logger.error('Failed to upsert symbol', {
        instrumentToken: symbolData.instrumentToken,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw this.handleRepositoryError(error, 'upsertSymbol');
    }
  }

  /**
   * Batch upsert multiple symbols for high-performance bulk operations
   */
  async batchUpsertSymbols(
    symbols: SymbolMasterUpsert[], 
    batchSize: number = 500
  ): Promise<{ 
    totalProcessed: number; 
    totalAdded: number; 
    totalUpdated: number; 
    errors: Array<{ symbol: string; error: string }> 
  }> {
    const startTime = this.dateTimeUtils.getTime();
    let totalProcessed = 0;
    let totalAdded = 0;
    let totalUpdated = 0;
    const errors: Array<{ symbol: string; error: string }> = [];

    this.logger.log(`Starting batch upsert of ${symbols.length} symbols`, {
      batchSize,
      totalSymbols: symbols.length,
    });

    try {
      // Process symbols in batches
      for (let i = 0; i < symbols.length; i += batchSize) {
        const batch = symbols.slice(i, i + batchSize);
        
        this.logger.debug(`Processing batch ${Math.floor(i / batchSize) + 1}`, {
          batchStart: i,
          batchSize: batch.length,
          totalBatches: Math.ceil(symbols.length / batchSize),
        });

        // Process each symbol in the batch
        for (const symbol of batch) {
          try {
            const result = await this.upsertSymbol(symbol);
            totalProcessed++;
            
            if (result.isNew) {
              totalAdded++;
            } else {
              totalUpdated++;
            }
          } catch (error) {
            errors.push({
              symbol: symbol.instrumentToken || symbol.tradingSymbol,
              error: this.errorUtils.getErrorMessage(error),
            });
            this.logger.warn('Failed to process symbol in batch', {
              instrumentToken: symbol.instrumentToken,
              tradingSymbol: symbol.tradingSymbol,
              error: this.errorUtils.getErrorMessage(error),
            });
          }
        }
      }

      const duration = this.dateTimeUtils.getTime() - startTime;
      
      this.logger.log('Batch upsert completed', {
        totalSymbols: symbols.length,
        totalProcessed,
        totalAdded,
        totalUpdated,
        errorCount: errors.length,
        duration: `${duration}ms`,
        symbolsPerSecond: Math.round((totalProcessed / duration) * 1000),
      });

      return {
        totalProcessed,
        totalAdded,
        totalUpdated,
        errors,
      };
    } catch (error) {
      this.logger.error('Batch upsert failed', {
        totalSymbols: symbols.length,
        processedSoFar: totalProcessed,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw this.handleRepositoryError(error, 'batchUpsertSymbols');
    }
  }

  /**
   * Transform and upsert Kite Connect raw instrument data
   */
  async upsertKiteInstrumentData(kiteData: KiteInstrumentMasterRaw[]): Promise<{
    totalProcessed: number;
    totalAdded: number;
    totalUpdated: number;
    errors: Array<{ symbol: string; error: string }>;
  }> {
    try {
      this.logger.log(`Transforming and upserting ${kiteData.length} Kite instruments`);

      // Validate and transform Kite data to internal format
      const transformedSymbols: SymbolMasterUpsert[] = [];
      const transformErrors: Array<{ symbol: string; error: string }> = [];

      for (const kiteInstrument of kiteData) {
        try {
          // Validate Kite data
          const validatedKiteData = KiteInstrumentMasterRawSchema.parse(kiteInstrument);
          
          // Transform to internal format
          const symbolData: SymbolMasterUpsert = {
            instrumentToken: validatedKiteData.instrument_token,
            exchangeToken: validatedKiteData.exchange_token,
            tradingSymbol: validatedKiteData.tradingsymbol,
            name: validatedKiteData.name,
            lastPrice: validatedKiteData.last_price,
            expiry: validatedKiteData.expiry ? new Date(validatedKiteData.expiry) : undefined,
            strike: validatedKiteData.strike,
            tickSize: validatedKiteData.tick_size,
            lotSize: validatedKiteData.lot_size,
            instrumentType: validatedKiteData.instrument_type,
            segment: validatedKiteData.segment,
            exchange: validatedKiteData.exchange,
            isActive: true,
          };

          transformedSymbols.push(symbolData);
        } catch (error) {
          transformErrors.push({
            symbol: kiteInstrument.tradingsymbol || kiteInstrument.instrument_token || 'unknown',
            error: this.errorUtils.getErrorMessage(error),
          });
        }
      }

      this.logger.log(`Transformed ${transformedSymbols.length} symbols, ${transformErrors.length} errors`);

      // Batch upsert the transformed symbols
      const result = await this.batchUpsertSymbols(transformedSymbols);

      return {
        totalProcessed: result.totalProcessed,
        totalAdded: result.totalAdded,
        totalUpdated: result.totalUpdated,
        errors: [...transformErrors, ...result.errors],
      };
    } catch (error) {
      this.logger.error('Failed to upsert Kite instrument data', {
        totalInstruments: kiteData.length,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw this.handleRepositoryError(error, 'upsertKiteInstrumentData');
    }
  }

  // ==================== QUERY OPERATIONS ====================

  /**
   * Find symbol by instrument token
   */
  async findByInstrumentToken(instrumentToken: string): Promise<SymbolMaster | null> {
    try {
      this.logger.debug('Finding symbol by instrument token', { instrumentToken });

      const result = await this.questdbService.executeQuery<SymbolMaster>(
        SymbolMasterQueries.GET_SYMBOL_BY_TOKEN,
        [instrumentToken]
      );

      if (result.rowCount === 0) {
        return null;
      }

      const symbolData = result.data[0];
      return this.transformQuestDBToSymbolMaster(symbolData);
    } catch (error) {
      this.logger.error('Failed to find symbol by instrument token', {
        instrumentToken,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw this.handleRepositoryError(error, 'findByInstrumentToken');
    }
  }

  /**
   * Find symbols with filters and pagination
   */
  async findSymbols(filters: SymbolMasterQueryFilters): Promise<{
    data: SymbolMaster[];
    total: number;
    hasMore: boolean;
  }> {
    try {
      // Validate filters
      const validatedFilters = SymbolMasterQueryFiltersSchema.parse(filters);
      
      this.logger.debug('Finding symbols with filters', validatedFilters);

      // Build dynamic query based on filters
      let query = SymbolMasterQueries.GET_SYMBOLS_BY_FILTERS;
      const params: unknown[] = [];
      const conditions: string[] = ['is_active = true'];

      // Add filter conditions
      if (validatedFilters.exchange) {
        conditions.push(`exchange = $${params.length + 1}`);
        params.push(validatedFilters.exchange);
      }

      if (validatedFilters.segment) {
        conditions.push(`segment = $${params.length + 1}`);
        params.push(validatedFilters.segment);
      }

      if (validatedFilters.instrumentType) {
        conditions.push(`instrument_type = $${params.length + 1}`);
        params.push(validatedFilters.instrumentType);
      }

      if (validatedFilters.tradingSymbol) {
        conditions.push(`trading_symbol ILIKE $${params.length + 1}`);
        params.push(`%${validatedFilters.tradingSymbol}%`);
      }

      if (validatedFilters.isActive !== undefined) {
        conditions.push(`is_active = $${params.length + 1}`);
        params.push(validatedFilters.isActive);
      }

      // Add WHERE clause if we have conditions
      if (conditions.length > 0) {
        query += ` AND ${conditions.join(' AND ')}`;
      }

      // Add ordering and pagination
      query += ` ORDER BY trading_symbol LIMIT $${params.length + 1} OFFSET $${params.length + 2}`;
      params.push(validatedFilters.limit, validatedFilters.offset);

      const result = await this.questdbService.executeQuery<SymbolMaster>(query, params);

      // Transform results
      const symbols = result.data.map(row => this.transformQuestDBToSymbolMaster(row));

      // Get total count for pagination
      let countQuery = `SELECT COUNT(*) as total FROM ${SYMBOL_MASTER_TABLE} WHERE timestamp = (SELECT MAX(timestamp) FROM ${SYMBOL_MASTER_TABLE} s2 WHERE s2.instrument_token = ${SYMBOL_MASTER_TABLE}.instrument_token)`;
      const countParams: unknown[] = [];
      
      if (conditions.length > 0) {
        countQuery += ` AND ${conditions.join(' AND ')}`;
        countParams.push(...params.slice(0, -2)); // Exclude limit and offset
      }

      const countResult = await this.questdbService.executeQuery<{ total: number }>(countQuery, countParams);
      const total = countResult.data[0]?.total || 0;
      const hasMore = validatedFilters.offset + validatedFilters.limit < total;

      return {
        data: symbols,
        total,
        hasMore,
      };
    } catch (error) {
      this.logger.error('Failed to find symbols', {
        filters,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw this.handleRepositoryError(error, 'findSymbols');
    }
  }

  // ==================== UTILITY METHODS ====================

  /**
   * Transform QuestDB row data to SymbolMaster schema
   */
  private transformQuestDBToSymbolMaster(row: any): SymbolMaster {
    return SymbolMasterSchema.parse({
      instrumentToken: row.instrument_token,
      exchangeToken: row.exchange_token,
      tradingSymbol: row.trading_symbol,
      name: row.name,
      lastPrice: row.last_price,
      expiry: row.expiry ? new Date(row.expiry) : undefined,
      strike: row.strike,
      tickSize: row.tick_size,
      lotSize: row.lot_size,
      instrumentType: row.instrument_type,
      segment: row.segment,
      exchange: row.exchange,
      isActive: row.is_active,
      downloadedAt: new Date(row.downloaded_at),
      updatedAt: new Date(row.updated_at),
    });
  }

  /**
   * Handle repository errors consistently
   */
  private handleRepositoryError(error: unknown, operation: string): Error {
    if (error instanceof QuestDBError) {
      return new Error(`QuestDB operation failed in ${operation}: ${error.message}`);
    }
    
    if (error instanceof Error) {
      return new Error(`Repository operation failed in ${operation}: ${error.message}`);
    }
    
    return new Error(`Unknown error in ${operation}: ${String(error)}`);
  }

  /**
   * Get repository statistics
   */
  async getStats(): Promise<{
    totalRecords: number;
    activeRecords: number;
    lastUpdate: Date | null;
    exchangeBreakdown: Record<string, number>;
    segmentBreakdown: Record<string, number>;
  }> {
    try {
      return await this.tableManager.getTableStats();
    } catch (error) {
      this.logger.error('Failed to get repository stats', {
        error: this.errorUtils.getErrorMessage(error),
      });
      throw this.handleRepositoryError(error, 'getStats');
    }
  }

  /**
   * Clean up old data
   */
  async cleanupOldData(daysToKeep: number = 30): Promise<number> {
    try {
      return await this.tableManager.cleanupOldData(daysToKeep);
    } catch (error) {
      this.logger.error('Failed to cleanup old data', {
        daysToKeep,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw this.handleRepositoryError(error, 'cleanupOldData');
    }
  }

  /**
   * Search symbols by trading symbol pattern
   */
  async searchSymbols(
    searchTerm: string,
    limit: number = 50,
    offset: number = 0
  ): Promise<SymbolMaster[]> {
    try {
      this.logger.debug('Searching symbols', { searchTerm, limit, offset });

      const result = await this.questdbService.executeQuery<SymbolMaster>(
        SymbolMasterQueries.SEARCH_SYMBOLS,
        [`%${searchTerm}%`, limit, offset]
      );

      return result.data.map(row => this.transformQuestDBToSymbolMaster(row));
    } catch (error) {
      this.logger.error('Failed to search symbols', {
        searchTerm,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw this.handleRepositoryError(error, 'searchSymbols');
    }
  }

  /**
   * Deactivate symbols not present in current download
   * Used for cleanup after symbol master download
   */
  async deactivateMissingSymbols(
    exchange: string,
    segment: string,
    activeInstrumentTokens: string[]
  ): Promise<number> {
    try {
      this.logger.debug('Deactivating missing symbols', {
        exchange,
        segment,
        activeTokensCount: activeInstrumentTokens.length,
      });

      const now = this.dateTimeUtils.getUtcNow();
      const tokenList = activeInstrumentTokens.map(token => `'${token}'`).join(',');

      const result = await this.questdbService.executeQuery(
        SymbolMasterQueries.DEACTIVATE_MISSING_SYMBOLS,
        [now.toISOString(), exchange, segment, tokenList]
      );

      const deactivatedCount = result.rowCount || 0;

      this.logger.log('Deactivated missing symbols', {
        exchange,
        segment,
        deactivatedCount,
      });

      return deactivatedCount;
    } catch (error) {
      this.logger.error('Failed to deactivate missing symbols', {
        exchange,
        segment,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw this.handleRepositoryError(error, 'deactivateMissingSymbols');
    }
  }

  /**
   * Get symbols by exchange and segment
   */
  async getSymbolsByExchangeSegment(
    exchange: string,
    segment: string,
    limit: number = 1000,
    offset: number = 0
  ): Promise<SymbolMaster[]> {
    try {
      this.logger.debug('Getting symbols by exchange and segment', {
        exchange,
        segment,
        limit,
        offset,
      });

      const query = `
        SELECT * FROM ${SYMBOL_MASTER_TABLE}
        WHERE exchange = $1
        AND segment = $2
        AND is_active = true
        AND timestamp = (
          SELECT MAX(timestamp) FROM ${SYMBOL_MASTER_TABLE} s2
          WHERE s2.instrument_token = ${SYMBOL_MASTER_TABLE}.instrument_token
        )
        ORDER BY trading_symbol
        LIMIT $3 OFFSET $4
      `;

      const result = await this.questdbService.executeQuery<SymbolMaster>(
        query,
        [exchange, segment, limit, offset]
      );

      return result.data.map(row => this.transformQuestDBToSymbolMaster(row));
    } catch (error) {
      this.logger.error('Failed to get symbols by exchange and segment', {
        exchange,
        segment,
        error: this.errorUtils.getErrorMessage(error),
      });
      throw this.handleRepositoryError(error, 'getSymbolsByExchangeSegment');
    }
  }

  /**
   * Get health status of the repository
   */
  async getHealthStatus(): Promise<{
    isHealthy: boolean;
    tableExists: boolean;
    connectionStatus: boolean;
    lastUpdate: Date | null;
    recordCount: number;
  }> {
    try {
      const tableExists = await this.tableManager.validateTable();
      const stats = await this.getStats();

      return {
        isHealthy: tableExists && stats.totalRecords >= 0,
        tableExists,
        connectionStatus: true,
        lastUpdate: stats.lastUpdate,
        recordCount: stats.totalRecords,
      };
    } catch (error) {
      this.logger.error('Failed to get health status', {
        error: this.errorUtils.getErrorMessage(error),
      });

      return {
        isHealthy: false,
        tableExists: false,
        connectionStatus: false,
        lastUpdate: null,
        recordCount: 0,
      };
    }
  }
}
