import { pgTable, varchar, text, integer, boolean, jsonb, timestamp, index } from 'drizzle-orm/pg-core';
import { baseModel } from '@app/common/models';
import { SymbolOperationTypeEnum } from './symbol.schema';

// ==================== SYMBOL AUDIT LOG TABLE ====================

/**
 * Symbol audit log table for tracking all symbol-related operations
 * 
 * This table stores comprehensive audit logs for all symbol operations including:
 * - Symbol master data downloads
 * - Individual symbol updates
 * - Batch processing operations
 * - Error tracking and debugging
 * 
 * Features:
 * - Full audit trail for compliance and debugging
 * - Structured JSON metadata for flexible data storage
 * - Indexed for fast querying by operation type, status, and time
 * - Integration with existing audit logging patterns
 */
export const SymbolAuditLogTable = pgTable(
  'symbol_audit_logs',
  {
    // Base model fields (id, createdAt, updatedAt, createdBy, updatedBy)
    ...baseModel,

    // Operation identification
    operationType: varchar('operation_type', { length: 50 }).notNull()
      .$type<'DOWNLOAD_STARTED' | 'DOWNLOAD_COMPLETED' | 'DOWNLOAD_FAILED' | 'SYMBOL_ADDED' | 'SYMBOL_UPDATED' | 'SYMBOL_DEACTIVATED' | 'BATCH_PROCESSED' | 'CLEANUP_PERFORMED'>(),
    
    // Context information
    exchange: varchar('exchange', { length: 10 }),
    segment: varchar('segment', { length: 20 }),
    instrumentToken: varchar('instrument_token', { length: 50 }),
    
    // Request tracking
    requestId: varchar('request_id', { length: 100 }),
    jobId: varchar('job_id', { length: 100 }),
    
    // Operation status
    status: varchar('status', { length: 20 }).notNull()
      .$type<'SUCCESS' | 'FAILED' | 'IN_PROGRESS'>(),
    
    // Operation details
    details: jsonb('details').$type<Record<string, unknown>>(),
    errorMessage: text('error_message'),
    
    // Performance metrics
    duration: integer('duration'), // Duration in milliseconds
    recordsAffected: integer('records_affected'),
    
    // Additional metadata
    userAgent: varchar('user_agent', { length: 500 }),
    ipAddress: varchar('ip_address', { length: 45 }),
    
    // Timestamps
    operationStartedAt: timestamp('operation_started_at', { withTimezone: true }),
    operationCompletedAt: timestamp('operation_completed_at', { withTimezone: true }),
  },
  (table) => ({
    // Indexes for efficient querying
    operationTypeIdx: index('idx_symbol_audit_operation_type').on(table.operationType),
    statusIdx: index('idx_symbol_audit_status').on(table.status),
    exchangeIdx: index('idx_symbol_audit_exchange').on(table.exchange),
    segmentIdx: index('idx_symbol_audit_segment').on(table.segment),
    instrumentTokenIdx: index('idx_symbol_audit_instrument_token').on(table.instrumentToken),
    requestIdIdx: index('idx_symbol_audit_request_id').on(table.requestId),
    jobIdIdx: index('idx_symbol_audit_job_id').on(table.jobId),
    createdAtIdx: index('idx_symbol_audit_created_at').on(table.createdAt),
    
    // Composite indexes for common query patterns
    operationStatusIdx: index('idx_symbol_audit_operation_status').on(table.operationType, table.status),
    exchangeSegmentIdx: index('idx_symbol_audit_exchange_segment').on(table.exchange, table.segment),
    dateRangeIdx: index('idx_symbol_audit_date_range').on(table.createdAt, table.status),
  })
);

// ==================== SYMBOL DOWNLOAD STATISTICS TABLE ====================

/**
 * Symbol download statistics table for tracking download performance and metrics
 * 
 * This table stores aggregated statistics for symbol download operations:
 * - Daily download summaries
 * - Performance metrics
 * - Success/failure rates
 * - Data quality metrics
 */
export const SymbolDownloadStatsTable = pgTable(
  'symbol_download_stats',
  {
    // Base model fields
    ...baseModel,

    // Date and scope
    downloadDate: timestamp('download_date', { withTimezone: true }).notNull(),
    exchange: varchar('exchange', { length: 10 }).notNull(),
    segment: varchar('segment', { length: 20 }).notNull(),
    
    // Download metrics
    totalSymbols: integer('total_symbols').notNull().default(0),
    symbolsAdded: integer('symbols_added').notNull().default(0),
    symbolsUpdated: integer('symbols_updated').notNull().default(0),
    symbolsDeactivated: integer('symbols_deactivated').notNull().default(0),
    symbolsSkipped: integer('symbols_skipped').notNull().default(0),
    
    // Performance metrics
    downloadDuration: integer('download_duration'), // Duration in milliseconds
    processingDuration: integer('processing_duration'), // Duration in milliseconds
    averageProcessingTime: integer('average_processing_time'), // Per symbol in milliseconds
    
    // Quality metrics
    successRate: integer('success_rate'), // Percentage (0-100)
    errorCount: integer('error_count').notNull().default(0),
    
    // Status tracking
    downloadStatus: varchar('download_status', { length: 20 }).notNull()
      .$type<'COMPLETED' | 'FAILED' | 'PARTIAL'>(),
    
    // Additional metadata
    kiteApiVersion: varchar('kite_api_version', { length: 20 }),
    dataSourceUrl: varchar('data_source_url', { length: 500 }),
    checksumMd5: varchar('checksum_md5', { length: 32 }),
    
    // Error details
    errorDetails: jsonb('error_details').$type<Record<string, unknown>>(),
    
    // Timestamps
    downloadStartedAt: timestamp('download_started_at', { withTimezone: true }),
    downloadCompletedAt: timestamp('download_completed_at', { withTimezone: true }),
  },
  (table) => ({
    // Indexes for efficient querying
    downloadDateIdx: index('idx_symbol_stats_download_date').on(table.downloadDate),
    exchangeIdx: index('idx_symbol_stats_exchange').on(table.exchange),
    segmentIdx: index('idx_symbol_stats_segment').on(table.segment),
    statusIdx: index('idx_symbol_stats_status').on(table.downloadStatus),
    
    // Composite indexes
    dateExchangeIdx: index('idx_symbol_stats_date_exchange').on(table.downloadDate, table.exchange),
    exchangeSegmentIdx: index('idx_symbol_stats_exchange_segment').on(table.exchange, table.segment),
    
    // Unique constraint for one record per date/exchange/segment
    uniqueDownloadRecord: index('idx_symbol_stats_unique').on(table.downloadDate, table.exchange, table.segment),
  })
);

// ==================== SYMBOL OPERATION QUEUE TABLE ====================

/**
 * Symbol operation queue table for tracking queued operations
 * 
 * This table stores information about queued symbol operations:
 * - Scheduled downloads
 * - Manual operations
 * - Retry attempts
 * - Operation dependencies
 */
export const SymbolOperationQueueTable = pgTable(
  'symbol_operation_queue',
  {
    // Base model fields
    ...baseModel,

    // Operation identification
    operationType: varchar('operation_type', { length: 50 }).notNull(),
    operationId: varchar('operation_id', { length: 100 }).notNull().unique(),
    
    // Queue information
    queueName: varchar('queue_name', { length: 100 }).notNull(),
    jobId: varchar('job_id', { length: 100 }),
    
    // Operation parameters
    parameters: jsonb('parameters').$type<Record<string, unknown>>(),
    
    // Scheduling
    scheduledAt: timestamp('scheduled_at', { withTimezone: true }).notNull(),
    priority: integer('priority').notNull().default(5), // 1=highest, 10=lowest
    
    // Status tracking
    status: varchar('status', { length: 20 }).notNull()
      .$type<'QUEUED' | 'PROCESSING' | 'COMPLETED' | 'FAILED' | 'CANCELLED'>(),
    
    // Execution tracking
    startedAt: timestamp('started_at', { withTimezone: true }),
    completedAt: timestamp('completed_at', { withTimezone: true }),
    
    // Retry logic
    attemptCount: integer('attempt_count').notNull().default(0),
    maxAttempts: integer('max_attempts').notNull().default(3),
    nextRetryAt: timestamp('next_retry_at', { withTimezone: true }),
    
    // Results and errors
    result: jsonb('result').$type<Record<string, unknown>>(),
    errorMessage: text('error_message'),
    errorDetails: jsonb('error_details').$type<Record<string, unknown>>(),
    
    // Dependencies
    dependsOn: varchar('depends_on', { length: 100 }), // Operation ID this depends on
    blockedBy: varchar('blocked_by', { length: 100 }), // Operation ID blocking this
    
    // Metadata
    requestedBy: varchar('requested_by', { length: 100 }),
    tags: jsonb('tags').$type<string[]>(),
  },
  (table) => ({
    // Indexes for efficient querying
    operationTypeIdx: index('idx_symbol_queue_operation_type').on(table.operationType),
    statusIdx: index('idx_symbol_queue_status').on(table.status),
    scheduledAtIdx: index('idx_symbol_queue_scheduled_at').on(table.scheduledAt),
    priorityIdx: index('idx_symbol_queue_priority').on(table.priority),
    queueNameIdx: index('idx_symbol_queue_name').on(table.queueName),
    jobIdIdx: index('idx_symbol_queue_job_id').on(table.jobId),
    
    // Composite indexes for queue processing
    statusPriorityIdx: index('idx_symbol_queue_status_priority').on(table.status, table.priority),
    queueStatusIdx: index('idx_symbol_queue_queue_status').on(table.queueName, table.status),
    
    // Dependency tracking
    dependsOnIdx: index('idx_symbol_queue_depends_on').on(table.dependsOn),
    blockedByIdx: index('idx_symbol_queue_blocked_by').on(table.blockedBy),
  })
);

// ==================== TYPE EXPORTS ====================

/**
 * TypeScript types derived from Drizzle table schemas
 */
export type SymbolAuditLog = typeof SymbolAuditLogTable.$inferSelect;
export type NewSymbolAuditLog = typeof SymbolAuditLogTable.$inferInsert;

export type SymbolDownloadStats = typeof SymbolDownloadStatsTable.$inferSelect;
export type NewSymbolDownloadStats = typeof SymbolDownloadStatsTable.$inferInsert;

export type SymbolOperationQueue = typeof SymbolOperationQueueTable.$inferSelect;
export type NewSymbolOperationQueue = typeof SymbolOperationQueueTable.$inferInsert;

// ==================== TABLE COLLECTION ====================

/**
 * Collection of all symbol-related database tables
 */
export const SymbolTables = {
  SymbolAuditLog: SymbolAuditLogTable,
  SymbolDownloadStats: SymbolDownloadStatsTable,
  SymbolOperationQueue: SymbolOperationQueueTable,
} as const;

// ==================== MIGRATION HELPERS ====================

/**
 * SQL statements for creating symbol-related tables
 * These can be used in Drizzle migrations
 */
export const SymbolTableMigrations = {
  createAuditLogTable: `
    CREATE TABLE IF NOT EXISTS symbol_audit_logs (
      id SERIAL PRIMARY KEY,
      operation_type VARCHAR(50) NOT NULL,
      exchange VARCHAR(10),
      segment VARCHAR(20),
      instrument_token VARCHAR(50),
      request_id VARCHAR(100),
      job_id VARCHAR(100),
      status VARCHAR(20) NOT NULL,
      details JSONB,
      error_message TEXT,
      duration INTEGER,
      records_affected INTEGER,
      user_agent VARCHAR(500),
      ip_address VARCHAR(45),
      operation_started_at TIMESTAMPTZ,
      operation_completed_at TIMESTAMPTZ,
      created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      created_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM',
      updated_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM'
    );
  `,
  
  createDownloadStatsTable: `
    CREATE TABLE IF NOT EXISTS symbol_download_stats (
      id SERIAL PRIMARY KEY,
      download_date TIMESTAMPTZ NOT NULL,
      exchange VARCHAR(10) NOT NULL,
      segment VARCHAR(20) NOT NULL,
      total_symbols INTEGER NOT NULL DEFAULT 0,
      symbols_added INTEGER NOT NULL DEFAULT 0,
      symbols_updated INTEGER NOT NULL DEFAULT 0,
      symbols_deactivated INTEGER NOT NULL DEFAULT 0,
      symbols_skipped INTEGER NOT NULL DEFAULT 0,
      download_duration INTEGER,
      processing_duration INTEGER,
      average_processing_time INTEGER,
      success_rate INTEGER,
      error_count INTEGER NOT NULL DEFAULT 0,
      download_status VARCHAR(20) NOT NULL,
      kite_api_version VARCHAR(20),
      data_source_url VARCHAR(500),
      checksum_md5 VARCHAR(32),
      error_details JSONB,
      download_started_at TIMESTAMPTZ,
      download_completed_at TIMESTAMPTZ,
      created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      created_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM',
      updated_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM'
    );
  `,
  
  createOperationQueueTable: `
    CREATE TABLE IF NOT EXISTS symbol_operation_queue (
      id SERIAL PRIMARY KEY,
      operation_type VARCHAR(50) NOT NULL,
      operation_id VARCHAR(100) NOT NULL UNIQUE,
      queue_name VARCHAR(100) NOT NULL,
      job_id VARCHAR(100),
      parameters JSONB,
      scheduled_at TIMESTAMPTZ NOT NULL,
      priority INTEGER NOT NULL DEFAULT 5,
      status VARCHAR(20) NOT NULL,
      started_at TIMESTAMPTZ,
      completed_at TIMESTAMPTZ,
      attempt_count INTEGER NOT NULL DEFAULT 0,
      max_attempts INTEGER NOT NULL DEFAULT 3,
      next_retry_at TIMESTAMPTZ,
      result JSONB,
      error_message TEXT,
      error_details JSONB,
      depends_on VARCHAR(100),
      blocked_by VARCHAR(100),
      requested_by VARCHAR(100),
      tags JSONB,
      created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
      created_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM',
      updated_by VARCHAR(100) NOT NULL DEFAULT 'SYSTEM'
    );
  `,
} as const;
