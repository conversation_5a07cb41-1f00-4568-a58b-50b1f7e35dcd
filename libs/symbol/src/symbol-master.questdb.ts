/**
 * QuestDB Symbol Master Table Schema
 * 
 * This file defines the QuestDB table structure for storing symbol master data
 * from Zerodha Kite Connect API. QuestDB is optimized for time-series data
 * and provides excellent performance for symbol data operations.
 * 
 * Features:
 * - Time-series optimized storage
 * - Deduplication support
 * - Efficient querying by timestamp and symbol attributes
 * - Proper indexing for fast lookups
 */

import type { QuestDBService } from '@app/core/questdb';
import { Logger } from '@nestjs/common';

/**
 * QuestDB table name for symbol master data
 */
export const SYMBOL_MASTER_TABLE = 'symbol_master';

/**
 * QuestDB table creation SQL for symbol master data
 * 
 * Key features:
 * - Uses timestamp as designated timestamp column for time-series optimization
 * - Includes all Kite Connect instrument master fields
 * - Optimized for fast queries and upserts
 * - Supports deduplication on instrument_token + timestamp
 */
export const CREATE_SYMBOL_MASTER_TABLE_SQL = `
CREATE TABLE IF NOT EXISTS ${SYMBOL_MASTER_TABLE} (
    -- Primary identifiers
    instrument_token STRING NOT NULL,
    exchange_token STRING NOT NULL,
    
    -- Symbol information
    trading_symbol STRING NOT NULL,
    name STRING,
    
    -- Market data
    last_price DOUBLE,
    tick_size DOUBLE NOT NULL,
    lot_size INT NOT NULL,
    
    -- Derivatives specific
    expiry TIMESTAMP,
    strike DOUBLE,
    
    -- Classification
    instrument_type STRING NOT NULL,
    segment STRING NOT NULL,
    exchange STRING NOT NULL,
    
    -- Status and metadata
    is_active BOOLEAN DEFAULT true,
    downloaded_at TIMESTAMP NOT NULL,
    updated_at TIMESTAMP NOT NULL,
    
    -- Time-series timestamp (designated timestamp column)
    timestamp TIMESTAMP
) timestamp(timestamp) PARTITION BY DAY DEDUP UPSERT KEYS(instrument_token, timestamp);
`;

/**
 * Index creation SQL for optimized queries
 */
export const CREATE_SYMBOL_MASTER_INDEXES_SQL = [
  // Index on instrument_token for fast lookups
  `ALTER TABLE ${SYMBOL_MASTER_TABLE} ADD INDEX idx_instrument_token (instrument_token);`,
  
  // Index on trading_symbol for symbol searches
  `ALTER TABLE ${SYMBOL_MASTER_TABLE} ADD INDEX idx_trading_symbol (trading_symbol);`,
  
  // Index on exchange for exchange-specific queries
  `ALTER TABLE ${SYMBOL_MASTER_TABLE} ADD INDEX idx_exchange (exchange);`,
  
  // Index on segment for segment-specific queries
  `ALTER TABLE ${SYMBOL_MASTER_TABLE} ADD INDEX idx_segment (segment);`,
  
  // Index on instrument_type for type-specific queries
  `ALTER TABLE ${SYMBOL_MASTER_TABLE} ADD INDEX idx_instrument_type (instrument_type);`,
  
  // Composite index for common query patterns
  `ALTER TABLE ${SYMBOL_MASTER_TABLE} ADD INDEX idx_exchange_segment (exchange, segment);`,
  
  // Index on is_active for filtering active symbols
  `ALTER TABLE ${SYMBOL_MASTER_TABLE} ADD INDEX idx_is_active (is_active);`,
];

/**
 * QuestDB table initialization and management class
 */
export class SymbolMasterQuestDBTable {
  private readonly logger = new Logger(SymbolMasterQuestDBTable.name);

  constructor(private readonly questdbService: QuestDBService) {}

  /**
   * Initialize the symbol master table and indexes
   */
  async initialize(): Promise<void> {
    try {
      this.logger.log('Initializing symbol master table in QuestDB');

      // Create the main table
      await this.questdbService.executeQuery(CREATE_SYMBOL_MASTER_TABLE_SQL);
      this.logger.log('Symbol master table created successfully');

      // Create indexes for optimized queries
      for (const indexSql of CREATE_SYMBOL_MASTER_INDEXES_SQL) {
        try {
          await this.questdbService.executeQuery(indexSql);
        } catch (error) {
          // Indexes might already exist, log warning but continue
          this.logger.warn(`Index creation warning: ${error instanceof Error ? error.message : 'Unknown error'}`);
        }
      }

      this.logger.log('Symbol master table indexes created successfully');
    } catch (error) {
      this.logger.error('Failed to initialize symbol master table', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Check if the table exists and is properly configured
   */
  async validateTable(): Promise<boolean> {
    try {
      // Check if table exists
      const tableCheckResult = await this.questdbService.executeQuery(`
        SELECT table_name 
        FROM information_schema.tables 
        WHERE table_name = '${SYMBOL_MASTER_TABLE}'
      `);

      if (tableCheckResult.rowCount === 0) {
        this.logger.warn('Symbol master table does not exist');
        return false;
      }

      // Check table structure
      const columnCheckResult = await this.questdbService.executeQuery(`
        SELECT column_name, data_type 
        FROM information_schema.columns 
        WHERE table_name = '${SYMBOL_MASTER_TABLE}'
        ORDER BY ordinal_position
      `);

      const expectedColumns = [
        'instrument_token',
        'exchange_token',
        'trading_symbol',
        'name',
        'last_price',
        'tick_size',
        'lot_size',
        'expiry',
        'strike',
        'instrument_type',
        'segment',
        'exchange',
        'is_active',
        'downloaded_at',
        'updated_at',
        'timestamp',
      ];

      const actualColumns = columnCheckResult.data.map((row: any) => row.column_name);
      const missingColumns = expectedColumns.filter(col => !actualColumns.includes(col));

      if (missingColumns.length > 0) {
        this.logger.error('Symbol master table is missing required columns', {
          missingColumns,
        });
        return false;
      }

      this.logger.log('Symbol master table validation successful');
      return true;
    } catch (error) {
      this.logger.error('Failed to validate symbol master table', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      return false;
    }
  }

  /**
   * Get table statistics for monitoring
   */
  async getTableStats(): Promise<{
    totalRecords: number;
    activeRecords: number;
    lastUpdate: Date | null;
    exchangeBreakdown: Record<string, number>;
    segmentBreakdown: Record<string, number>;
  }> {
    try {
      // Get total and active record counts
      const countResult = await this.questdbService.executeQuery(`
        SELECT 
          COUNT(*) as total_records,
          COUNT(CASE WHEN is_active = true THEN 1 END) as active_records,
          MAX(updated_at) as last_update
        FROM ${SYMBOL_MASTER_TABLE}
      `);

      // Get exchange breakdown
      const exchangeResult = await this.questdbService.executeQuery(`
        SELECT exchange, COUNT(*) as count
        FROM ${SYMBOL_MASTER_TABLE}
        WHERE is_active = true
        GROUP BY exchange
        ORDER BY count DESC
      `);

      // Get segment breakdown
      const segmentResult = await this.questdbService.executeQuery(`
        SELECT segment, COUNT(*) as count
        FROM ${SYMBOL_MASTER_TABLE}
        WHERE is_active = true
        GROUP BY segment
        ORDER BY count DESC
      `);

      const stats = countResult.data[0] || { total_records: 0, active_records: 0, last_update: null };
      
      const exchangeBreakdown: Record<string, number> = {};
      exchangeResult.data.forEach((row: any) => {
        exchangeBreakdown[row.exchange] = parseInt(row.count, 10);
      });

      const segmentBreakdown: Record<string, number> = {};
      segmentResult.data.forEach((row: any) => {
        segmentBreakdown[row.segment] = parseInt(row.count, 10);
      });

      return {
        totalRecords: parseInt(stats.total_records, 10),
        activeRecords: parseInt(stats.active_records, 10),
        lastUpdate: stats.last_update ? new Date(stats.last_update) : null,
        exchangeBreakdown,
        segmentBreakdown,
      };
    } catch (error) {
      this.logger.error('Failed to get table statistics', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Clean up old data (older than specified days)
   */
  async cleanupOldData(daysToKeep: number = 30): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const result = await this.questdbService.executeQuery(`
        DELETE FROM ${SYMBOL_MASTER_TABLE}
        WHERE timestamp < '${cutoffDate.toISOString()}'
      `);

      const deletedRows = result.rowCount || 0;
      this.logger.log(`Cleaned up ${deletedRows} old symbol master records`, {
        cutoffDate: cutoffDate.toISOString(),
        daysToKeep,
      });

      return deletedRows;
    } catch (error) {
      this.logger.error('Failed to cleanup old data', {
        error: error instanceof Error ? error.message : 'Unknown error',
        daysToKeep,
      });
      throw error;
    }
  }

  /**
   * Optimize table performance (vacuum and reindex)
   */
  async optimizeTable(): Promise<void> {
    try {
      this.logger.log('Starting table optimization');

      // QuestDB automatically optimizes tables, but we can trigger compaction
      await this.questdbService.executeQuery(`
        ALTER TABLE ${SYMBOL_MASTER_TABLE} RESUME WAL
      `);

      this.logger.log('Table optimization completed');
    } catch (error) {
      this.logger.error('Failed to optimize table', {
        error: error instanceof Error ? error.message : 'Unknown error',
      });
      throw error;
    }
  }

  /**
   * Get the table name
   */
  getTableName(): string {
    return SYMBOL_MASTER_TABLE;
  }
}

/**
 * SQL query templates for common operations
 */
export const SymbolMasterQueries = {
  // Upsert symbol data with deduplication
  UPSERT_SYMBOL: `
    INSERT INTO ${SYMBOL_MASTER_TABLE} (
      instrument_token, exchange_token, trading_symbol, name,
      last_price, tick_size, lot_size, expiry, strike,
      instrument_type, segment, exchange, is_active,
      downloaded_at, updated_at, timestamp
    ) VALUES (
      $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16
    )
  `,

  // Get symbols by filters
  GET_SYMBOLS_BY_FILTERS: `
    SELECT * FROM ${SYMBOL_MASTER_TABLE}
    WHERE timestamp = (
      SELECT MAX(timestamp) FROM ${SYMBOL_MASTER_TABLE} s2 
      WHERE s2.instrument_token = ${SYMBOL_MASTER_TABLE}.instrument_token
    )
  `,

  // Get symbol by instrument token
  GET_SYMBOL_BY_TOKEN: `
    SELECT * FROM ${SYMBOL_MASTER_TABLE}
    WHERE instrument_token = $1
    AND timestamp = (
      SELECT MAX(timestamp) FROM ${SYMBOL_MASTER_TABLE} s2 
      WHERE s2.instrument_token = $1
    )
    LIMIT 1
  `,

  // Search symbols by trading symbol
  SEARCH_SYMBOLS: `
    SELECT * FROM ${SYMBOL_MASTER_TABLE}
    WHERE trading_symbol ILIKE $1
    AND is_active = true
    AND timestamp = (
      SELECT MAX(timestamp) FROM ${SYMBOL_MASTER_TABLE} s2 
      WHERE s2.instrument_token = ${SYMBOL_MASTER_TABLE}.instrument_token
    )
    ORDER BY trading_symbol
    LIMIT $2 OFFSET $3
  `,

  // Deactivate symbols not in current download
  DEACTIVATE_MISSING_SYMBOLS: `
    UPDATE ${SYMBOL_MASTER_TABLE} 
    SET is_active = false, updated_at = $1
    WHERE exchange = $2 
    AND segment = $3
    AND instrument_token NOT IN ($4)
    AND is_active = true
  `,
} as const;
