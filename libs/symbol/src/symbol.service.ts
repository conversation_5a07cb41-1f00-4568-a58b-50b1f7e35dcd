import { Injectable, Logger } from '@nestjs/common';
import { DateTimeUtilsService, ErrorUtilsService } from '@app/utils';
import { EnvService } from '@app/core/env';
import { KiteConnect } from 'kiteconnect';
import {
  KiteInstrumentMasterRaw,
  KiteInstrumentMasterRawSchema,
  SymbolMasterUpsert,
  SymbolMaster,
  SymbolDownloadResult,
} from './symbol.schema';
import { SymbolMasterRepository } from './symbol-master.repository';

/**
 * Symbol service for managing symbol master data with Kite Connect integration
 *
 * Provides comprehensive symbol master data management including:
 * - Fetching instrument master data from Zerodha Kite Connect API
 * - Data transformation and validation
 * - Repository operations with QuestDB storage
 * - Error handling and retry mechanisms
 * - Performance monitoring and logging
 */
@Injectable()
export class SymbolService {
  private readonly logger = new Logger(SymbolService.name);
  private readonly maxRetries = 3;
  private readonly retryDelay = 1000; // 1 second

  constructor(
    private readonly dateTimeUtils: DateTimeUtilsService,
    private readonly errorUtils: ErrorUtilsService,
    private readonly envService: EnvService,
    private readonly symbolRepository: SymbolMasterRepository,
  ) {}

  // ==================== KITE CONNECT INTEGRATION ====================

  /**
   * Get KiteConnect instance with proper configuration
   */
  private getKiteConnectInstance(apiKey?: string, accessToken?: string): KiteConnect {
    const kiteApiKey = apiKey || this.envService.get('ZERODHA_API_KEY');

    if (!kiteApiKey) {
      throw new Error('Zerodha API key not configured');
    }

    const kite = new KiteConnect({
      api_key: kiteApiKey,
    });

    if (accessToken) {
      kite.setAccessToken(accessToken);
    }

    return kite;
  }

  /**
   * Fetch instrument master data from Kite Connect API
   * Downloads the complete instrument list as CSV and parses it
   */
  async fetchKiteInstrumentMaster(exchange?: string): Promise<KiteInstrumentMasterRaw[]> {
    const startTime = this.dateTimeUtils.getTime();

    try {
      this.logger.log('Fetching instrument master from Kite Connect API', { exchange });

      // Get KiteConnect instance (no access token needed for instruments API)
      const kite = this.getKiteConnectInstance();

      // Fetch instruments data
      let instruments: any[];
      if (exchange) {
        this.logger.debug(`Fetching instruments for exchange: ${exchange}`);
        instruments = await this.retryOperation(() => kite.getInstruments(exchange));
      } else {
        this.logger.debug('Fetching instruments for all exchanges');
        instruments = await this.retryOperation(() => kite.getInstruments());
      }

      this.logger.log(`Fetched ${instruments.length} instruments from Kite API`, {
        exchange: exchange || 'ALL',
        duration: `${this.dateTimeUtils.getTime() - startTime}ms`,
      });

      // Transform and validate the data
      const validatedInstruments: KiteInstrumentMasterRaw[] = [];
      const errors: string[] = [];

      for (const instrument of instruments) {
        try {
          const validatedInstrument = KiteInstrumentMasterRawSchema.parse(instrument);
          validatedInstruments.push(validatedInstrument);
        } catch (error) {
          errors.push(`Invalid instrument data: ${this.errorUtils.getErrorMessage(error)}`);
        }
      }

      if (errors.length > 0) {
        this.logger.warn(`Found ${errors.length} invalid instruments`, {
          sampleErrors: errors.slice(0, 5),
        });
      }

      this.logger.log(`Validated ${validatedInstruments.length} instruments`, {
        totalFetched: instruments.length,
        validCount: validatedInstruments.length,
        errorCount: errors.length,
      });

      return validatedInstruments;
    } catch (error) {
      this.logger.error('Failed to fetch instrument master from Kite API', {
        exchange,
        error: this.errorUtils.getErrorMessage(error),
        duration: `${this.dateTimeUtils.getTime() - startTime}ms`,
      });
      throw new Error(`Failed to fetch Kite instrument master: ${this.errorUtils.getErrorMessage(error)}`);
    }
  }

  /**
   * Fetch all symbols from all exchanges using Kite Connect API
   */
  async fetchAllSymbols(): Promise<KiteInstrumentMasterRaw[]> {
    this.logger.log('Fetching all symbols from all exchanges via Kite Connect');
    return this.fetchKiteInstrumentMaster();
  }

  /**
   * Fetch symbols by exchange and segment
   * Mock implementation - replace with actual broker API calls
   */
  fetchSymbolsByExchange(
    exchange: string,
    segment: string,
  ): Array<{
    symbol: string;
    name: string;
    exchange: string;
    segment: string;
    instrumentToken?: string;
    lotSize?: number;
    tickSize?: number;
  }> {
    this.logger.log(`Fetching symbols for ${exchange}:${segment}`);

    // Mock data - replace with actual API calls
    return [
      {
        symbol: 'RELIANCE',
        name: 'Reliance Industries Limited',
        exchange,
        segment,
        instrumentToken: '738561',
        lotSize: 1,
        tickSize: 0.05,
      },
    ];
  }

  /**
   * Upsert symbol data
   * Mock implementation - replace with actual database operations
   */
  upsertSymbol(
    symbolData: {
      symbol: string;
      name: string;
      exchange: string;
      segment: string;
      instrumentToken?: string;
      lotSize?: number;
      tickSize?: number;
    },
    _forceRefresh: boolean,
  ): { isNew: boolean } {
    this.logger.debug(`Upserting symbol: ${symbolData.symbol}`);

    // Mock implementation - replace with actual database operations
    // For now, randomly return new or updated
    const isNew = Math.random() > 0.5;

    return { isNew };
  }

  /**
   * Update last sync timestamp for exchange/segment
   * Mock implementation - replace with actual database operations
   */
  updateLastSyncTimestamp(exchange: string, segment: string): void {
    this.logger.log(`Updating last sync timestamp for ${exchange}:${segment}`);

    // Mock implementation - replace with actual database operations
    // This would typically update a sync_status table
  }

  /**
   * Cleanup stale symbols for exchange/segment
   * Mock implementation - replace with actual database operations
   */
  cleanupStaleSymbols(exchange: string, segment: string): void {
    this.logger.log(`Cleaning up stale symbols for ${exchange}:${segment}`);

    // Mock implementation - replace with actual database operations
    // This would typically mark symbols as inactive if they weren't updated in the latest sync
  }
}
