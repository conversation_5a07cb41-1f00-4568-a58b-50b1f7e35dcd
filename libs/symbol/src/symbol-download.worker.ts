import { Injectable } from '@nestjs/common';
import { Job } from 'bullmq';
import { BaseWorkerService, QueueService, QueueHealthService, QueueNameEnum, type JobResult } from '@app/core/queue';
import { DateTimeUtilsService } from '@app/utils';
import { EnvService } from '@app/core/env';
import { createQueueConnection, DEFAULT_WORKER_OPTIONS } from '@app/core/queue/queue.config';
import { SymbolService } from './symbol.service';
import type { SymbolDownloadJobDataType } from './symbol-download.queue';

/**
 * Symbol download worker
 * Processes symbol master data download jobs
 */
@Injectable()
export class SymbolDownloadWorkerService extends BaseWorkerService<SymbolDownloadJobDataType> {
  constructor(
    private readonly queueService: QueueService,
    private readonly queueHealthService: QueueHealthService,
    private readonly symbolService: SymbolService,
    private readonly envService: EnvService,
    protected readonly dateTimeUtils: DateTimeUtilsService,
  ) {
    const connectionConfig = createQueueConnection(envService);
    super(
      QueueNameEnum.enum.SYMBOL_DOWNLOAD,
      connectionConfig,
      {
        ...DEFAULT_WORKER_OPTIONS,
        concurrency: 2, // Process 2 jobs concurrently
      },
      dateTimeUtils,
    );

    // Register health check
    this.queueHealthService.registerWorkerHealthCheck(SymbolDownloadWorkerService.name, () => this.getHealthStatus());
  }

  /**
   * Process symbol download job
   */
  protected async processJob(job: Job<SymbolDownloadJobDataType>): Promise<
    JobResult<{
      exchange: string;
      segment: string;
      symbolsProcessed: number;
      symbolsAdded: number;
      symbolsUpdated: number;
      duration: number;
    }>
  > {
    const startTime = Date.now();
    const { exchange, segment, forceRefresh, batchSize, requestId } = job.data;

    this.logger.log(`Processing symbol download job for ${exchange}:${segment}`, {
      jobId: job.id,
      exchange,
      segment,
      forceRefresh,
      batchSize,
      requestId,
    });

    try {
      // Update progress to 10%
      await job.updateProgress(10);

      // Step 1: Fetch symbols from broker/exchange
      this.logger.log(`Fetching symbols from ${exchange}:${segment}`);
      const symbols = this.fetchSymbolsFromExchange(exchange, segment);

      // Update progress to 30%
      await job.updateProgress(30);

      // Step 2: Process symbols in batches
      let symbolsProcessed = 0;
      let symbolsAdded = 0;
      let symbolsUpdated = 0;
      const totalSymbols = symbols.length;

      this.logger.log(`Processing ${totalSymbols} symbols in batches of ${batchSize}`);

      for (let i = 0; i < symbols.length; i += batchSize) {
        const batch = symbols.slice(i, i + batchSize);

        // Process batch
        const batchResult = this.processBatch(batch, forceRefresh);
        symbolsAdded += batchResult.added;
        symbolsUpdated += batchResult.updated;
        symbolsProcessed += batch.length;

        // Update progress
        const progress = 30 + Math.floor((symbolsProcessed / totalSymbols) * 60);
        await job.updateProgress(progress);

        this.logger.debug(
          `Processed batch ${Math.floor(i / batchSize) + 1}, symbols: ${symbolsProcessed}/${totalSymbols}`,
        );
      }

      // Update progress to 95%
      await job.updateProgress(95);

      // Step 3: Cleanup and finalize
      this.finalizeSymbolDownload(exchange, segment);

      const duration = Date.now() - startTime;

      this.logger.log(`Symbol download completed for ${exchange}:${segment}`, {
        jobId: job.id,
        symbolsProcessed,
        symbolsAdded,
        symbolsUpdated,
        duration,
      });

      return {
        success: true,
        data: {
          exchange,
          segment,
          symbolsProcessed,
          symbolsAdded,
          symbolsUpdated,
          duration,
        },
        jobId: job.id || 'unknown',
        queueName: this.queueName,
        processedAt: this.dateTimeUtils.getUtcNow(),
        duration,
        attempts: job.attemptsMade,
      };
    } catch (error) {
      this.logger.error(`Symbol download failed for ${exchange}:${segment}`, {
        jobId: job.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        exchange,
        segment,
      });

      throw error; // Re-throw to let BullMQ handle retries
    }
  }

  /**
   * Fetch symbols from exchange/broker
   */
  private fetchSymbolsFromExchange(
    exchange: string,
    segment: string,
  ): Array<{
    symbol: string;
    name: string;
    exchange: string;
    segment: string;
    instrumentToken?: string;
    lotSize?: number;
    tickSize?: number;
  }> {
    // This is a mock implementation
    // In real implementation, this would call the broker API or exchange API

    if (exchange === 'ALL' && segment === 'ALL') {
      // Handle daily download for all exchanges
      return this.symbolService.fetchAllSymbols();
    }

    return this.symbolService.fetchSymbolsByExchange(exchange, segment);
  }

  /**
   * Process a batch of symbols
   */
  private processBatch(
    symbols: Array<{
      symbol: string;
      name: string;
      exchange: string;
      segment: string;
      instrumentToken?: string;
      lotSize?: number;
      tickSize?: number;
    }>,
    forceRefresh: boolean,
  ): { added: number; updated: number } {
    let added = 0;
    let updated = 0;

    for (const symbol of symbols) {
      try {
        const result = this.symbolService.upsertSymbol(symbol, forceRefresh);
        if (result.isNew) {
          added++;
        } else {
          updated++;
        }
      } catch (error) {
        this.logger.warn(`Failed to process symbol ${symbol.symbol}:`, error);
        // Continue processing other symbols
      }
    }

    return { added, updated };
  }

  /**
   * Finalize symbol download process
   */
  private finalizeSymbolDownload(exchange: string, segment: string): void {
    // Update last sync timestamp
    this.symbolService.updateLastSyncTimestamp(exchange, segment);

    // Cleanup stale symbols if needed
    if (exchange !== 'ALL') {
      this.symbolService.cleanupStaleSymbols(exchange, segment);
    }
  }

  /**
   * Handle job completion
   */
  protected onJobCompleted(job: Job<SymbolDownloadJobDataType>, result: JobResult<unknown>): void {
    this.logger.log(`Symbol download job completed successfully`, {
      jobId: job.id,
      exchange: job.data.exchange,
      segment: job.data.segment,
      result,
    });
  }

  /**
   * Handle job failure
   */
  protected onJobFailed(job: Job<SymbolDownloadJobDataType> | undefined, error: Error): void {
    if (job) {
      this.logger.error(`Symbol download job failed`, {
        jobId: job.id,
        exchange: job.data.exchange,
        segment: job.data.segment,
        error: error.message,
        attempts: job.attemptsMade,
      });
    } else {
      this.logger.error(`Symbol download job failed (job undefined)`, {
        error: error.message,
      });
    }
  }

  /**
   * Handle job progress updates
   */
  protected onJobProgress(job: Job<SymbolDownloadJobDataType>, progress: number | object | string): void {
    this.logger.debug(`Symbol download job progress`, {
      jobId: job.id,
      exchange: job.data.exchange,
      segment: job.data.segment,
      progress,
    });
  }

  /**
   * Handle stalled jobs
   */
  protected onJobStalled(jobId: string): void {
    this.logger.warn(`Symbol download job stalled`, { jobId });
  }

  /**
   * Handle worker errors
   */
  protected onWorkerError(error: Error): void {
    this.logger.error(`Symbol download worker error`, { error: error.message });
  }

  /**
   * Cleanup on module destroy
   */
  async onModuleDestroy(): Promise<void> {
    // Unregister health check
    this.queueHealthService.unregisterWorkerHealthCheck(SymbolDownloadWorkerService.name);

    // Call parent cleanup
    await super.onModuleDestroy();
  }
}
